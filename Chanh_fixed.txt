const targetSrc2 = "data:image/webp;base64,UklGRi4BAABXRUJQVlA4TCIBAAAvEUAHEF9gJJKUV5/g5zUKh0NTi86UoIaaSLaan9pExgAVHtBKhSIEKW0kBTpehcOhUfRfFiWcEAgkgW2/WADAbUuZQ13NNCtsvNv0l/Ra/8JzAMfZ/vTNZwXH/y9JO/Nu6efAWMn+FVbDRYJlhgMwTOgrRL2fDxHRfwFBEWsAcA1FOvUBQLOOrqYZNrFbDIPUbJTJ55EYaBZ+PDA1rC04GTt5wOH/0EqUk/bh+x7bZFx7bSGToTOpdulleRSs6x++u9cjezPaPHznwwczfti8XLhba9NRG0E2g8RZWycTxnvOAJZlebRdA3Qwr0c1ANCbhw8AgLV83y7s5NwsaJdXFYCK2K2SAogy94hQEeXcJVIV0VdeLdEXESlQGVAVFMoVAA==";
const gochanh = "https://sunflower-land.com/game-assets/fruit/dead_tree.webp";
const goca = "https://sunflower-land.com/game-assets/fruit/bush_shrub.png";
const flemon = "data:image/webp;base64,UklGRi4BAABXRUJQVlA4TCIBAAAvEUAHEF9gJAASXkAk2bzgawe4gEneMBTZxv+4uhoFtBVAFsJRG0lqgy+CIaVBuiY1ikWQbVOx0R/sAYD/f0ZW5rOLTjOlNCP63IZc2y6Ao+1/2ub3+ME1W8p+/uVOeACD5MJEkuID2LpAmcl2aMuoW5TpeHXvENH/CQDAbDSGRfqP1HZoI2QQve08hdCsbA2VjbBQ7G3IbaCnNBdvQ9y3hkIV6tlvPw9Lobn0v3477UhybZ+fdSaecyHT+7P691eUGYeoPq/4UXczVOy8K/SVn3NdZrw49cNcsXJbciDn9cQDs4EwryaeMxtAyD6vtgFg1Zx38XfPDPyGUJoVC0BATM6TBRAZJogQEBmzS2QFRCOz0kQjIrJAzUDQYAEIiMgCAA==";
const targetSrc4 = "https://sunflower-land.com/game-assets/crops/soil2.png";
//const ftomato = "data:image/webp;base64,UklGRi4BAABXRUJQVlA4TCIBAAAvEUAHEF9gJJKUV5/g5zUKh0NTi86UoIaaSLaan9pExgAVHtBKhSIEKW0kBTpehcOhUfRfFiWcEAgkgW2/WADAbUuZQ13NNCtsvNv0l/Ra/8JzAMfZ/vTNZwXH/y9JO/Nu6efAWMn+FVbDRYJlhgMwTOgrRL2fDxHRfwFBEWsAcA1FOvUBQLOOrqYZNrFbDIPUbJTJ55EYaBZ+PDA1rC04GTt5wOH/0EqUk/bh+x7bZFx7bSGToTOTOpdulleRSs6x++u9cjezPaPHznwwczfti8XLhba9NRG0E2g8RZWycTxnvOAJZlebRdA3Qwr0c1ANCbhw8AgLV83y7s5NwsaJdXFYCK2K2SAogy94hQEeXcJVIV0VdeLdEXESlQGVAVFMoVAA==";
const ftomato = "data:image/webp;base64,UklGRi4BAABXRUJQVlA4TCIBAAAvEUAHEF9gJJKUV5/g5zUKh0NTi86UoIaaSLaan9pExgAVHtBKhSIEKW0kBTpehcOhUfRfFiWcEAgkgW2/WADAbUuZQ13NNCtsvNv0l/Ra/8JzAMfZ/vTNZwXH/y9JO/Nu6efAWMn+FVbDRYJlhgMwTOgrRL2fDxHRfwFBEWsAcA1FOvUBQLOOrqYZNrFbDIPUbJTJ55EYaBZ+PDA1rC04GTt5wOH/0EqUk/bh+x7bZFx7bSGToTOpdulleRSs6x++u9cjezPaPHznwwczfti8XLhba9NRG0E2g8RZWycTxnvOAJZlebRdA3Qwr0c1ANCbhw8AgLV83y7s5NwsaJdXFYCK2K2SAogy94hQEeXcJVIV0VdeLdEXESlQGVAVFMoVAA==";
const apple = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAsAAAAOCAYAAAD5YeaVAAAAAXNSR0IArs4c6QAAAOVJREFUKJGNkb1qAkEURs8VY6/gI7hWKURQsBiIlU3eIFj5GOmsfIetxDLNljYWW1hsEBEr1y6VICipBIW9Fu4sJuygp5qfw/dxZ+ABZtRTM+opgDwSa+YFgG14cctW3IYXAHbrM4U80fvoqk27p5gnv/+c+H4tsVufs7N4MpNMHntNtetVWvsZ/QLQjxeSJY+9pnarZQAqnRYEU4btNw7VKN2j/Xghci+6qHRafAXT/AH/c5hHtwF9SWB/BCCvYZbe+ZI8l2z583Q2xYUAmHpDB+ou8SUh3Cwl+25Tb6hLDjdLAbgCt2JPqfeFVTkAAAAASUVORK5CYII=";
const banana = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAAOCAYAAAD0f5bSAAAAAXNSR0IArs4c6QAAAM5JREFUKJGFka8OgzAQxr+SEoKdwaNmlkyQbFWYvdAS0heYIzO8xRRPMDMFcsk8HoMlGG6i0BRo6JnL3X2/+/oHcESWCspSQWaPu4DbOZoqQc9PxZyQEikXBSvQcx3vcWkBAO9vq3u70FgmFNxTDc6Z7QEQJ6D6QWcAQ93ZnTRghlHzjdgUTNvXwa1iE7As8MYyIVn4y+EOAAB8qDsA0XJgc4R6hDBvGAOAXsYUXA+QhY/8dbRunwF9pzBvWC9BG0cLAKz+qZcxbYhpqVn/Adu2YYOCjYxqAAAAAElFTkSuQmCC";
const tomato = "data:image/webp;base64,UklGRowAAABXRUJQVlA4TH8AAAAvCYACEE9gqAEhJXxNAQVMmOtf4moohCTJOYGcT+dY3vpl1DSSAt0dT48KHFDiXwUVQuY/ALDdRpdJ4uY6998/4ZXaANPa2pt8Klh2qLZ7ZuBfICrxtO/X1H+FbB7R/5gBQMYCEJlWtNNvmSx3UmmupP1vpjnNoicZcZIOPgQAAA==";
//const lemon = "data:image/webp;base64,UklGRpAAAABXRUJQVlA4TIMAAAAvCUACEFegKLYN6iUxCuhfwU4LBf5WaiiOJANaBOCnZCH/0G5GAiAhsYPGeTY4w+gEm9n5DwD+fyJiy30zOZrLQ8SJ1NgG2ES27eT6r5DnhIyPR/EPA6ukxNhwQkICEpjuh/cCZw/EPEc4vydC6LrkJSHcvc6egLUfqyWBh9scC+xwA=";
const lemon = "data:image/webp;base64,UklGRpAAAABXRUJQVlA4TIMAAAAvCUACEFegKLYN6iUxCuhfwU4LBf5WaiiOJANaBOCnZCH/0G5GAiAhsYPGeTY4w+gEm9n5DwD+fyJiy30zOZrLQ8SJ1NgG2ES27eT8T6r5DnhIyPR/EPA6ukxNhwQkICEpjuh/cCZw/EPEc4vydC6LrkJSHcvc6egLUfqyWBh9scC+xwA=";
const trai1 = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB8AAAAjCAYAAABsFtHvAAAAAXNSR0IArs4c6QAAA2dJREFUWIWtlkFLG1EQx/8r0aWQQlpIFSFqpHoytPWSFpoUhFbxVHr20K/QS3vxC1gPXj1LPoM0HgImhSQ9BMJ6CElxg7mkXVpDFUpUmh7WeTv79u1uog4Esu/tzO8/82ZeosHHsltrA/5c/PRF83v3phbxgy5kxwEAaSzbG1sYBImQxZIFiXZtZLfWBgTtmedifTWZBQBUUUOreOkJuLixMkitDxBLRoXYKmoAgFbx0leEJgewOjriiT5iySgAO/O8WcRqMou8WQQAWB1dBFvcWBlMpSaEDxcNQKxxHxfcD8yNApB1jQsAwFRqAgAQT/RhdXQsZMdd2ZMf7XMBY6Tc2NcQT/Q9JVc9cygZAQhKVYslo2I9nui7eiPCg1kdiJdkoCpruQJWR4fVsc+4hSrz1kVcbqLsQVmRo9XRBeysfSX27s9FXH4EkY9PblblGNBRbGTTYi1vFgWcg7kAajwCU/X8pmVMBU6tD8Sc582iAMuZqp75e7Fk1DUlVBU6d995VammNWPfWzCetd+4ymPnmXNypszJmQug8nMgz5aPFz/3VvHSJdBzvZJTz3QcedaA3eG2UAfMIfZ3uhccv3jCPZIRytruVHcWqnHjYyZDe+a5c0zJGhNAcCd+17hwNxy/aGQogevTbdSn2+LcSWAay57mIogsngR5up1A/ENBGtuz0J8/BAA03v1xCaiihipqiCWjgQK4ueDy5dI1LsSnsT0LlA30K7+hf3gFAMoKUAL8uZkraPwm7BoXaOYK2pi8yaHNXEFr5gpafboNAA6YRFxXgarTM8+VvwOcQXEBdrc3cwUN19csbbqsbHgjvkihXjnEE2MOgO7pF1uUk5Qc1zVqSiiAe5+Ptb8fMVBl7YXRGeuefdk8DTeK9XcOsVmZxOnBSUbVVLzEKhvqT2H80eOXAHDy/l+Jr/96+hYAsLNbxl7Dyjx4M1Oipg0DA4o/kCoo2WZlEq+fTToL1WPX+6cHJxlgpnTV6+Ps249MSE7+cBk8rNkCnBjWz+9f/d5VnnkQeCk9P5KYoFiezMMyPqoeewQspeeB3XJpr2GFljoQPowdSWcdZn7lv9Wo3dZGggeVddSSA4o5v2mXh5mq7MpL4K4F+I2b7w10VwKC5jzw+rutgCBwKPymIsKgI8GHFTEslOw/cNcyyfgTT8gAAAAASUVORK5CYII=";
const trai2 = "https://sunflower-land.com/game-assets/fruit/apple/apple_tree.png";
const gochuoi = "https://sunflower-land.com/game-assets/fruit/bush_shrub.png";
const gotao = "https://sunflower-land.com/game-assets/fruit/bush_shrub.png";
const specialTree = "https://sunflower-land.com/game-assets/resources/tree/Spring/autumn_spring_tree.webp";
let specialTreeChopCounter = 0;
let ironHarvestCounter = 0;
let tree = 1;
let iron = 0;

// Hàm kiểm tra tự động và chạy các tác vụ
async function startRandomInterval() {
  console.log("Đã khởi động chanh, cà chua");
  while (true) {
    await autoClickButtonsByText(["Try again", "Claim", "Continue"]);
    const randomDelayTime = Math.floor(Math.random() * 3456) + 3456;
    await new Promise((resolve) => setTimeout(resolve, randomDelayTime));

    if (tree > 0){
        await handleSpecialTreeChop();
        await randomDelayBetweenFunctions();
    }
    if (iron > 0){
      const { clearIronCount } = checkIronStatus();
      if (clearIronCount > 0) {
        await handleIronHarvest();
        await randomDelayBetweenFunctions();
      }
    }

    const cropImages = document.querySelectorAll(
      `img[src*='plant.png'], img[src='${targetSrc4}']`
    );
    if (cropImages.length > 0) {
      const seedSelected = await selectSeed([1, 2, 3, 4, 5]);
      if (seedSelected) {
        await handleCropPlanting();
        await randomDelayBetweenFunctions();
      }
    }

    const fruitImages = document.querySelectorAll(
      `img[src='${ftomato}'], img[src='${flemon}'], img[src='${gochanh}'], img[src='${goca}'], img[src='${targetSrc4}']`
    );
    if (fruitImages.length > 0) {
      await handleFruitHarvesting();
      await randomDelayBetweenFunctions();
    }
  }
}

// Hàm tạo độ trễ ngẫu nhiên từ 0,25 đến 0,65 giây
async function randomDelay() {
  await autoClickButtonsByText(["Try again", "Claim", "Continue"]);
  const delay = Math.floor(Math.random() * 200) + 250;
  await new Promise((resolve) => setTimeout(resolve, delay));
}

// Hàm tạo độ trễ ngẫu nhiên từ 0.25 đến 0.45 giây giữa các hàm
async function randomDelayBetweenFunctions() {
  const delay = Math.floor(Math.random() * 200) + 250;
  await new Promise((resolve) => setTimeout(resolve, delay));
}

async function autoClickButtonsByText(buttonTexts = ["Try again", "Claim", "Continue"]) {
  for (const text of buttonTexts) {
    const buttonDiv = Array.from(document.querySelectorAll("button div"))
      .find(div => div.textContent.trim().toLowerCase() === text.toLowerCase());

    if (buttonDiv) {
      const button = buttonDiv.closest("button");
      if (button && !button.disabled) {
        simulateRandomClick(button);
        await randomDelay();
      }
    }
  }
}

// Hàm mô phỏng click ngẫu nhiên
function simulateRandomClick(img) {
  const rect = img.getBoundingClientRect();
  const randomX = rect.left + Math.random() * rect.width;
  const randomY = rect.top + Math.random() * rect.height;
  img.dispatchEvent(
    new MouseEvent("click", {
      bubbles: true,
      clientX: randomX,
      clientY: randomY,
    })
  );
}

// Hàm kiểm tra trạng thái iron
function checkIronStatus() {
  const images = document.querySelectorAll("img[src='https://sunflower-land.com/game-assets/resources/iron_small.png']");
  let clearIronCount = 0;
  let fadedIronCount = 0;

  for (const img of images) {
    const parentContainer = img.closest(".absolute.w-full.h-full");
    if (parentContainer) {
      if (parentContainer.classList.contains("cursor-pointer")) {
        clearIronCount++;
      } else if (
        parentContainer.classList.contains("pointer-events-none") ||
        img.classList.contains("opacity-50")
      ) {
        fadedIronCount++;
      }
    }
  }

  return { clearIronCount, fadedIronCount };
}

// Hàm thu hoạch iron
async function handleIronHarvest() {
  ironHarvestCounter++;

  const images = document.querySelectorAll("img[src='https://sunflower-land.com/game-assets/resources/iron_small.png']");
  const clearIronImages = [];

  for (const img of images) {
    const parentContainer = img.closest(".absolute.w-full.h-full");
    if (parentContainer && parentContainer.classList.contains("cursor-pointer")) {
      clearIronImages.push(img);
    }
  }

  if (clearIronImages.length === 0) {
    return;
  }

  const shuffledIronImages = clearIronImages.sort(() => Math.random() - 0.5);
  for (const img of shuffledIronImages) {
    simulateRandomClick(img);
    await randomDelay();
  }
}

async function clickAtCoordinates(x, y, times) {
  for (let i = 0; i < times; i++) {
    document.elementFromPoint(x, y).dispatchEvent(
      new MouseEvent("click", {
        bubbles: true,
        cancelable: true,
        clientX: x,
        clientY: y,
      })
    );
    await randomDelay();
  }
}

async function handleSpecialTreeChop() {
  specialTreeChopCounter++;

  const specialTreeSources = [
    "https://sunflower-land.com/game-assets/resources/tree/Spring/autumn_spring_tree.webp",
    "https://sunflower-land.com/game-assets/resources/tree/Spring/winter_spring_tree.webp",
    "https://sunflower-land.com/game-assets/resources/tree/Spring/spring_spring_tree.webp",
    "https://sunflower-land.com/game-assets/resources/tree/Spring/summer_spring_tree.webp",
    "https://sunflower-land.com/game-assets/resources/tree/Desert/winter_desert_tree.webp",
    "https://sunflower-land.com/game-assets/resources/tree/Desert/summer_desert_tree.webp",
    "https://sunflower-land.com/game-assets/resources/tree/Desert/spring_desert_tree.webp",
    "https://sunflower-land.com/game-assets/resources/tree/Desert/autumn_desert_tree.webp"
  ];

  const images = Array.from(document.querySelectorAll("img"));
  const specialTreeImages = images.filter(img => specialTreeSources.includes(img.src));

  if (specialTreeImages.length === 0) {
    return;
  }

  for (const img of specialTreeImages) {
    await simulateRandomClick(img);
    await randomDelay();
  }
}

// Phần 1: Hàm xử lý cây trồng (plant.png) và đất trống
async function handleCropPlanting() {
  const images = document.querySelectorAll(
    `img[src*='plant.png'], img[src='${targetSrc4}']`
  );
  if (images.length === 0) {
    return;
  }

  const shuffledImages = Array.from(images).sort(() => Math.random() - 0.5);
  for (const img of shuffledImages) {
    const parent = img.closest("div");
    if (!parent) continue;

    if (img.src.includes("plant.png")) {
      simulateRandomClick(img);
      await randomDelay();

      const newImg = parent.querySelector("img");
      if (newImg && newImg.src.includes("soil2.png")) {
        simulateRandomClick(newImg);
        await randomDelay();
      } else {
        simulateRandomClick(img);
        await randomDelay();
      }
    } else if (img.src.includes("soil2.png")) {
      simulateRandomClick(img);
      await randomDelay();
    }

    const fixedContainer = document.querySelector(".fixed.inset-0.overflow-y-auto");
    if (fixedContainer) {
      await handleFixedElement();
      await randomDelay();
      await handleMoonSeekers();
    }
  }
}

// Phần 2: Hàm xử lý cây ăn quả, cây khô và đất trống
async function handleFruitHarvesting() {
  const images = document.querySelectorAll(
    `img[src='${ftomato}'], img[src='${flemon}'], img[src='${gochanh}'], img[src='${goca}'], img[src='${targetSrc4}']`
  );
  const harvestImages = [];
  const dryImages = [];
  const soilImages = [];

  for (const img of images) {
    if (img.src === ftomato || img.src === flemon) {
      harvestImages.push(img);
    } else if (img.src === gochanh || img.src === goca) {
      dryImages.push(img);
    } else if (img.src === targetSrc4) {
      soilImages.push(img);
    }
  }

  if (harvestImages.length === 0 && dryImages.length === 0 && soilImages.length === 0) {
    return;
  }

  if (harvestImages.length > 0) {
    const shuffledHarvestImages = harvestImages.sort(() => Math.random() - 0.5);
    for (const img of shuffledHarvestImages) {
      simulateRandomClick(img);
      await randomDelay();
    }
  }

  if (dryImages.length > 0) {
    const shuffledDryImages = dryImages.sort(() => Math.random() - 0.5);
    for (const img of shuffledDryImages) {
      simulateRandomClick(img);
      await randomDelay();
    }
  }

  const updatedImages = document.querySelectorAll(`img[src='${targetSrc4}']`);
  const updatedSoilImages = Array.from(updatedImages);

  if (updatedSoilImages.length > 0) {
    const marketImage = document.querySelector("img[src*='betty.gif']");
    if (!marketImage) {
      return;
    }

    simulateRandomClick(marketImage);
    await randomDelay();
    await randomDelay();

    const seedElements = document.querySelectorAll(
      ".fixed.inset-0.overflow-y-auto .flex.flex-wrap.mb-2 > .relative"
    );
    
    let seedSelected = false;
    for (const [index, seedElement] of Array.from(seedElements).entries()) {
      const cropImage = seedElement.querySelector("img");
      if (!cropImage) {
        continue;
      }

      if (cropImage.src === lemon || cropImage.src === tomato) {
        const stockDiv = seedElement.querySelector(
          ".w-fit.justify-center.flex.items-center.text-xs"
        );
        if (!stockDiv || stockDiv.textContent.trim() === "0") {
          continue;
        }

        simulateRandomClick(cropImage);
        seedSelected = true;
        await randomDelay();
        break;
      }
    }

    let marketClosed = false;
    let attempts = 0;
    const maxAttempts = 5;
    while (attempts < maxAttempts) {
      const closeButton = document.querySelector(
        "img[src*='close.png'], img[src*='icons/close'], .fixed button[class*='close'], .fixed img[alt*='close' i]"
      );
      if (closeButton) {
        closeButton.click();
        await randomDelay();
        marketClosed = true;
        break;
      }
      attempts++;
      await randomDelay();
    }

    if (seedSelected) {
      for (const img of updatedSoilImages) {
        simulateRandomClick(img);
        await randomDelay();
      }
    }
  }
}

// Hàm chọn hạt giống theo thứ tự
async function selectSeed(seedOrder) {
  const marketImage = document.querySelector("img[src*='betty.gif']");
  if (!marketImage) {
    return false;
  }

  const selectAndCloseMarket = async () => {
    simulateRandomClick(marketImage);
    await randomDelay();

    const seedElements = document.querySelectorAll(
      ".fixed.inset-0.overflow-y-auto .flex.flex-wrap.mb-2 > .relative"
    );
    if (seedElements.length === 0) {
      return false;
    }

    for (let seedIndex of seedOrder) {
      let seedElement = seedElements[seedIndex - 1];
      if (!seedElement) {
        continue;
      }

      let stockDiv = seedElement.querySelector(
        ".w-fit.justify-center.flex.items-center.text-xs"
      );
      if (!stockDiv || stockDiv.textContent.trim() === "0") {
        continue;
      }

      let cropImage = seedElement.querySelector("img[src*='crop.png']");
      if (!cropImage) {
        continue;
      }

      simulateRandomClick(cropImage);
      await randomDelay();

      const closeButton = document.querySelector(
        "img[src*='close.png'], img[src*='icons/close'], .fixed button[class*='close'], .fixed img[alt*='close' i]"
      );
      if (closeButton) {
        closeButton.click();
        await randomDelay();
        return true;
      }
      return false;
    }
    return false;
  };

  let success = await selectAndCloseMarket();
  while (!success) {
    success = await selectAndCloseMarket();
  }

  return true;
}

// Hàm xử lý menu đặc biệt
async function handleFixedElement() {
  const parent = document.querySelector(".fixed .relative.w-full.rounded-md");
  if (!parent) return;

  const imgs = parent.querySelectorAll("img");
  if (imgs.length < 2) return;

  const img2 = imgs[1];
  simulateRandomClick(img2);
  await randomDelay();
  clickCloseButton();
}

// Hàm xử lý Moon Seekers
async function handleMoonSeekers() {
  const moonSeekersSpan = document.querySelector(
    ".fixed .flex.flex-col.justify-center span.text-center.mb-2"
  );

  if (moonSeekersSpan) {
    const text = moonSeekersSpan.textContent.toLowerCase();
    let validSizes = [];
    if (text.includes("moon seekers")) {
      validSizes = [
        { width: 13, height: 16 },
        { width: 12, height: 16 },
        { width: 16, height: 17 },
        { width: 15, height: 16 },
        { width: 15, height: 17 },
        { width: 96, height: 64 },
        { width: 18, height: 16 },
        { width: 22, height: 25 },
        { width: 18, height: 29 },
        { width: 20, height: 19 },
        { width: 33, height: 28 },
        { width: 29, height: 28 },
        { width: 25, height: 25 },
      ];
    } else if (text.includes("goblins")) {
      validSizes = [
        { width: 96, height: 64 },
        { width: 18, height: 16 },
        { width: 22, height: 25 },
        { width: 18, height: 29 },
        { width: 20, height: 19 },
        { width: 33, height: 28 },
        { width: 29, height: 28 },
        { width: 25, height: 25 },
        { width: 26, height: 21 },
        { width: 18, height: 12 },
        { width: 25, height: 27 },
        { width: 24, height: 21 },
        { width: 19, height: 21 },
      ];
    }

    if (validSizes.length > 0) {
      const images = document.querySelectorAll(
        ".fixed .flex.flex-col.justify-center .flex.flex-wrap.justify-center.items-center img"
      );

      for (let img of images) {
        const width = img.naturalWidth;
        const height = img.naturalHeight;

        if (
          validSizes.some(
            (size) => size.width === width && size.height === height
          )
        ) {
          simulateRandomClick(img);
        }
      }
    }

    clickCloseButton();
  }
}

// Hàm đóng menu đặc biệt
function clickCloseButton() {
  setTimeout(() => {
    const closeButton = Array.from(
      document.querySelectorAll(".fixed button")
    ).find((btn) => btn.textContent.trim() === "Close");

    if (closeButton) {
      simulateRandomClick(closeButton);
    }
  }, 500);
}

// Chạy chương trình
startRandomInterval();
// ==UserScript==
// @name         Sunflower Land Auto Farm - Tự Động Hoàn <PERSON>
// @namespace    http://tampermonkey.net/
// @version      2025-07-25
// @description  Tự động farm trái cây, c<PERSON><PERSON> trồng, chặt cây và đào iron - Không cần nhập gì!
// <AUTHOR>
// @match        https://sunflower-land.com/play/
// @icon         https://www.google.com/s2/favicons?sz=64&domain=tampermonkey.net
// @grant        none
// ==/UserScript==

const targetSrc4 = "https://sunflower-land.com/game-assets/crops/soil2.png";

// Mảng source ảnh cây đặc biệt cần chặt
const specialTreeSources = [
    "https://sunflower-land.com/game-assets/resources/tree/Spring/autumn_spring_tree.webp",
    "https://sunflower-land.com/game-assets/resources/tree/Spring/winter_spring_tree.webp",
    "https://sunflower-land.com/game-assets/resources/tree/Spring/spring_spring_tree.webp",
    "https://sunflower-land.com/game-assets/resources/tree/Spring/summer_spring_tree.webp",
    "https://sunflower-land.com/game-assets/resources/tree/Basic/spring_basic_tree.webp",
    "https://sunflower-land.com/game-assets/resources/tree/Desert/winter_desert_tree.webp",
    "https://sunflower-land.com/game-assets/resources/tree/Desert/summer_desert_tree.webp",
    "https://sunflower-land.com/game-assets/resources/tree/Desert/spring_desert_tree.webp",
    "https://sunflower-land.com/game-assets/resources/tree/Desert/autumn_desert_tree.webp"
];

// Mảng source ảnh trái cây loại 1 (Chanh, Cà chua)
const fruitType1List = [
  "data:image/webp;base64,UklGRi4BAABXRUJQVlA4TCIBAAAvEUAHEF9gJAASXkAk2bzgawe4gEneMBTZxv+4uhoFtBVAFsJRG0lqgy+CIaVBuiY1ikWQbVOx0R/sAYD/f0ZW5rOLTjOlNCP63IZc2y6Ao+1/2ub3+ME1W8p+/uVOeACD5MJEkuID2LpAmcl2aMuoW5TpeHXvENH/CQDAbDSGRfqP1HZoI2QQve08hdCsbA2VjbBQ7G3IbaCnNBdvQ9y3hkIV6tlvPw9Lobn0v3477UhybZ+fdSaecyHT+7P691eUGYeoPq/4UXczVOy8K/SVn3NdZrw49cNcsXJbciDn9cQDs4EwryaeMxtAyD6vtgFg1Zx38XfPDPyGUJoVC0BATM6TBRAZJogQEBmzS2QFRCOz0kQjIrJAzUDQYAEIiMgCAA==",
  "data:image/webp;base64,UklGRi4BAABXRUJQVlA4TCIBAAAvEUAHEF9gJJKUV5/g5zUKh0NTi86UoIaaSLaan9pExgAVHtBKhSIEKW0kBTpehcOhUfRfFiWcEAgkgW2/WADAbUuZQ13NNCtsvNv0l/Ra/8JzAMfZ/vTNZwXH/y9JO/Nu6efAWMn+FVbDRYJlhgMwTOgrRL2fDxHRfwFBEWsAcA1FOvUBQLOOrqYZNrFbDIPUbJTJ55EYaBZ+PDA1rC04GTt5wOH/0EqUk/bh+x7bZFx7bSGToTOpdulleRSs6x++u9cjezPaPHznwwczfti8XLhba9NRG0E2g8RZWycTxnvOAJZlebRdA3Qwr0c1ANCbhw8AgLV83y7s5NwsaJdXFYCK2K2SAogy94hQEeXcJVIV0VdeLdEXESlQGVAVFMoVAA==",
];

// Mảng source ảnh trái cây loại 2 (Táo, Chuối, Việt quất)
const fruitType2List = [
  "https://sunflower-land.com/game-assets/fruit/apple/apple_tree.png",
  "https://sunflower-land.com/game-assets/fruit/banana/banana_tree.png",
  "https://sunflower-land.com/game-assets/fruit/blueberry/blueberry_bush.png",
];

// Mảng source ảnh cây khô cần chặt
const dryTreeSources = [
  "https://sunflower-land.com/game-assets/fruit/dead_tree.webp",
  "https://sunflower-land.com/game-assets/fruit/bush_shrub.png",
];

// Mảng thông tin hạt giống trái cây
const fruitSeeds = [
  {
    name: "Hạt Chanh",
    image: "data:image/webp;base64,UklGRpAAAABXRUJQVlA4TIMAAAAvCUACEFegKLYN6iUxCuhfwU4LBf5WaiiOJANaBOCnZCH/0G5GAiAhsYPGeTY4w+gEm9n5DwD+fyJiy30zOZrLQ8SJ1NgG2ES27eT8T6r5DnhIyPR/EPA6ukxNhwQkICEpjuh/cCZw/EPEc4vydC6LrkJSHcvc6egLUfqyWBh9scC+xwA=",
    type: 1
  },
  {
    name: "Hạt Cà Chua",
    image: "data:image/webp;base64,UklGRowAAABXRUJQVlA4TH8AAAAvCYACEE9gqAEhJXxNAQVMmOtf4moohCTJOYGcT+dY3vpl1DSSAt0dT48KHFDiXwUVQuY/ALDdRpdJ4uY6998/4ZXaANPa2pt8Klh2qLZ7ZuBfICrxtO/X1H+FbB7R/5gBQMYCEJlWtNNvmSx3UmmupP1vpjnNoicZcZIOPgQAAA==",
    type: 1
  },
  {
    name: "Hạt Táo",
    image: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAsAAAAOCAYAAAD5YeaVAAAAAXNSR0IArs4c6QAAAOVJREFUKJGNkb1qAkEURs8VY6/gI7hWKURQsBiIlU3eIFj5GOmsfIetxDLNljYWW1hsEBEr1y6VICipBIW9Fu4sJuygp5qfw/dxZ+ABZtRTM+opgDwSa+YFgG14cctW3IYXAHbrM4U80fvoqk27p5gnv/+c+H4tsVufs7N4MpNMHntNtetVWvsZ/QLQjxeSJY+9pnarZQAqnRYEU4btNw7VKN2j/Xghci+6qHRafAXT/AH/c5hHtwF9SWB/BCCvYZbe+ZI8l2z583Q2xYUAmHpDB+ou8SUh3Cwl+25Tb6hLDjdLAbgCt2JPqfeFVTkAAAAASUVORK5CYII=",
    type: 2
  },
  {
    name: "Hạt Chuối",
    image: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAAOCAYAAAD0f5bSAAAAAXNSR0IArs4c6QAAAM5JREFUKJGFka8OgzAQxr+SEoKdwaNmlkyQbFWYvdAS0heYIzO8xRRPMDMFcsk8HoMlGG6i0BRo6JnL3X2/+/oHcESWCspSQWaPu4DbOZoqQc9PxZyQEikXBSvQcx3vcWkBAO9vq3u70FgmFNxTDc6Z7QEQJ6D6QWcAQ93ZnTRghlHzjdgUTNvXwa1iE7As8MYyIVn4y+EOAAB8qDsA0XJgc4R6hDBvGAOAXsYUXA+QhY/8dbRunwF9pzBvWC9BG0cLAKz+qZcxbYhpqVn/Adu2YYOCjYxqAAAAAElFTkSuQmCC",
    type: 2
  },
  {
    name: "Hạt Việt Quất",
    image: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAAAXNSR0IArs4c6QAAAN9JREFUKJF1kTEKwjAYhb9IpYu3KL2AgyBCD6B0cPIAPUCXgpOTk5AlB/AATg6iByhIwcELFG/hEhziUKNJ1DeFvPe/935+QYBBtjb2fa9XIuSjUBxXpftlwiHhiZMp+nYCIE6mME/RUrHYXNiODwKgF0YCBCm09YOiyc07oWhys1uOOlcHNg1gODuTZn2ioslNWz88ga3mJl0lpNmlW/p6nHiklqpL27cfk6pkt1T0bBUtFVoq2LfEVekJXXhLhyTQGTi7vQdshX/QUnGvVyJ6Hca4LpYEvq7uHS4kf+EJiDFhlnltoFMAAAAASUVORK5CYII=",
    type: 2
  }
];

// Biến trạng thái - TẤT CẢ ĐÃ ĐƯỢC BẬT TỰ ĐỘNG
let specialTreeChopCounter = 0;
let ironHarvestCounter = 0;
let iron = 1; // Tự động bật đào iron
let tree = 1; // Tự động bật chặt cây
let plant = 1; // Tự động bật trồng trọt
let fruit = 1; // Tự động bật thu hoạch trái cây
let fruitType1 = 1; // Tự động bật trái cây loại 1 (Chanh, Cà chua)
let fruitType2 = 1; // Tự động bật trái cây loại 2 (Táo, Chuối, Việt quất)
let cropSeedOrderArray = [1, 2, 3, 4, 5]; // Thứ tự hạt giống cây trồng mặc định
let fruitSeedOrderArray = [1, 2, 3, 4, 5]; // Thứ tự hạt giống trái cây mặc định

// Tạo UI Panel hiển thị trạng thái
const uiPanel = document.createElement("div");
uiPanel.style.position = "fixed";
uiPanel.style.top = "20px";
uiPanel.style.right = "20px";
uiPanel.style.zIndex = "9999";
uiPanel.style.background = "#222";
uiPanel.style.color = "#fff";
uiPanel.style.padding = "10px";
uiPanel.style.borderRadius = "8px";
uiPanel.style.fontSize = "14px";
uiPanel.style.fontFamily = "Arial";
uiPanel.style.boxShadow = "0 0 10px rgba(0,0,0,0.5)";
uiPanel.innerHTML = `
  <div style="text-align:center; margin-bottom:10px; font-weight:bold; color:#4CAF50;">
    🤖 AUTO FARM ĐANG HOẠT ĐỘNG
  </div>
  <label><input type="checkbox" id="treeToggle" checked> 🌳 Chặt cây</label><br>
  <label><input type="checkbox" id="ironToggle" checked> ⛏️ Đào Iron</label><br>
  <label><input type="checkbox" id="fruitToggle" checked> 🍅 Trái cây</label><br>
  <div id="fruitTypeSelector" style="display:block; margin-left:8px;">
    <label><input type="checkbox" id="fruitType1Toggle" checked> 🍋 Trái cây loại 1 (Chanh, Cà chua)</label><br>
    <label><input type="checkbox" id="fruitType2Toggle" checked> 🍎 Trái cây loại 2 (Táo, Chuối, Việt quất)</label><br>
  </div>
  <label><input type="checkbox" id="plantToggle" checked> 🌱 Trồng trọt</label><br>
  <div style="margin-top:8px; font-size:12px; color:#ccc;">
    <div>✅ Tự động sử dụng hạt giống theo thứ tự 1→5</div>
    <div>✅ Không cần nhập gì thêm</div>
  </div>
`;
document.body.appendChild(uiPanel);

// Hàm tự động click các button đặc biệt
async function autoClickButtonsByText(buttonTexts = ["Try again", "Claim", "Continue"]) {
  for (const text of buttonTexts) {
    const buttonDiv = Array.from(document.querySelectorAll("button div"))
      .find(div => div.textContent.trim().toLowerCase() === text.toLowerCase());

    if (buttonDiv) {
      const button = buttonDiv.closest("button");
      if (button && !button.disabled) {
        simulateRandomClick(button);
        await randomDelay();
      }
    }
  }
}

// Event listeners cho UI controls
document.getElementById("treeToggle").addEventListener("change", function(e) {
  tree = e.target.checked ? 1 : 0;
  console.log("🌳 Tree trạng thái:", tree === 1 ? "BẬT" : "TẮT");
});

document.getElementById("ironToggle").addEventListener("change", function(e) {
  iron = e.target.checked ? 1 : 0;
  console.log("⛏️ Iron trạng thái:", iron === 1 ? "BẬT" : "TẮT");
});

const fruitTypeSection = document.getElementById("fruitTypeSelector");

document.getElementById("fruitToggle").addEventListener("change", function(e) {
  fruit = e.target.checked ? 1 : 0;
  console.log("🍅 Fruit trạng thái:", fruit === 1 ? "BẬT" : "TẮT");
  fruitTypeSection.style.display = fruit === 1 ? "block" : "none";
});

document.getElementById("fruitType1Toggle").addEventListener("change", function(e) {
  fruitType1 = e.target.checked ? 1 : 0;
  console.log("🍋 Loại 1 (Chanh, Cà chua):", fruitType1 ? "BẬT" : "TẮT");
});

document.getElementById("fruitType2Toggle").addEventListener("change", function(e) {
  fruitType2 = e.target.checked ? 1 : 0;
  console.log("🍎 Loại 2 (Táo, Chuối, Việt quất):", fruitType2 ? "BẬT" : "TẮT");
});

document.getElementById("plantToggle").addEventListener("change", function(e) {
  plant = e.target.checked ? 1 : 0;
  console.log("🌱 Plant trạng thái:", plant === 1 ? "BẬT" : "TẮT");
});

// Hàm kiểm tra tự động và chạy các tác vụ
async function startRandomInterval() {
  console.log("🤖 Đã khởi động AUTO FARM - Tất cả tính năng đã được bật tự động!");
  while (true) {
    // Tự động click các button đặc biệt trước
    await autoClickButtonsByText(["Try again", "Claim", "Continue"]);

    const randomDelayTime = Math.floor(Math.random() * 2389) + 3456;
    await new Promise((resolve) => setTimeout(resolve, randomDelayTime));

    if (tree > 0){
        await handleSpecialTreeChop();
        await randomDelayBetweenFunctions();
    }

    if (iron > 0){
      const { clearIronCount } = checkIronStatus();
      if (clearIronCount > 0) {
        await handleIronHarvest();
        await randomDelayBetweenFunctions();
      }
    }

    if (plant > 0) {
      const cropImages = document.querySelectorAll(
        `img[src*='plant.png'], img[src='${targetSrc4}']`
      );

      if (cropImages.length > 0) {
        await handleCropPlanting();
        await randomDelayBetweenFunctions();
      }
    }
    if (fruit > 0) {
      await handleFruitHarvesting();
      await randomDelayBetweenFunctions();
    }
  }
}

// Hàm tạo độ trễ ngẫu nhiên từ 0.25 đến 0.5 giây
async function randomDelay() {
  await autoClickButtonsByText(["Try again", "Claim", "Continue"]);
  const delay = Math.floor(Math.random() * 250) + 250;
  await new Promise((resolve) => setTimeout(resolve, delay));
}

// Hàm tạo độ trễ ngẫu nhiên từ 0.25 đến 0.5 giây giữa các hàm
async function randomDelayBetweenFunctions() {
  const delay = Math.floor(Math.random() * 250) + 250;
  await new Promise((resolve) => setTimeout(resolve, delay));
}

// Hàm mô phỏng click ngẫu nhiên
function simulateRandomClick(img) {
  const rect = img.getBoundingClientRect();
  const randomX = rect.left + Math.random() * rect.width;
  const randomY = rect.top + Math.random() * rect.height;
  img.dispatchEvent(
    new MouseEvent("click", {
      bubbles: true,
      clientX: randomX,
      clientY: randomY,
    })
  );
}

// Hàm kiểm tra trạng thái iron
function checkIronStatus() {
  const images = document.querySelectorAll("img[src='https://sunflower-land.com/game-assets/resources/iron_small.png']");
  let clearIronCount = 0;
  let fadedIronCount = 0;

  for (const img of images) {
    const parentContainer = img.closest(".absolute.w-full.h-full");
    if (parentContainer) {
      if (parentContainer.classList.contains("cursor-pointer")) {
        clearIronCount++;
      } else if (
        parentContainer.classList.contains("pointer-events-none") ||
        img.classList.contains("opacity-50")
      ) {
        fadedIronCount++;
      }
    }
  }

  return { clearIronCount, fadedIronCount };
}

// Hàm thu hoạch iron
async function handleIronHarvest() {
  ironHarvestCounter++;

  const images = document.querySelectorAll("img[src='https://sunflower-land.com/game-assets/resources/iron_small.png']");
  const clearIronImages = [];

  for (const img of images) {
    const parentContainer = img.closest(".absolute.w-full.h-full");
    if (parentContainer && parentContainer.classList.contains("cursor-pointer")) {
      clearIronImages.push(img);
    }
  }

  if (clearIronImages.length === 0) {
    return;
  }

  const shuffledIronImages = clearIronImages.sort(() => Math.random() - 0.5);
  for (const img of shuffledIronImages) {
    simulateRandomClick(img);
    await randomDelay();
  }
}

async function handleSpecialTreeChop() {
  specialTreeChopCounter++;

  const images = Array.from(document.querySelectorAll("img"));
  const specialTreeImages = images.filter(img => specialTreeSources.includes(img.src));

  if (specialTreeImages.length === 0) {
    return;
  }

  for (const img of specialTreeImages) {
    simulateRandomClick(img);
    await randomDelay();
  }
}

// Phần 1: Hàm xử lý cây trồng (plant.png) và đất trống
async function handleCropPlanting() {
  const images = document.querySelectorAll(
    `img[src*='plant.png'], img[src='${targetSrc4}']`
  );
  if (images.length === 0) {
    return;
  }

  // Thu hoạch cây trồng chín
  const plantImages = Array.from(images).filter(img => img.src.includes("plant.png"));
  for (const img of plantImages.sort(() => Math.random() - 0.5)) {
    simulateRandomClick(img);
    await randomDelay();
  }

  // Trồng lại nếu có đất trống và có seed order
  const soilImages = Array.from(images).filter(img => img.src.includes("soil2.png"));
  if (soilImages.length > 0 && cropSeedOrderArray.length > 0) {
    const seedSelected = await selectSeed(cropSeedOrderArray);
    if (seedSelected) {
      for (const img of soilImages.sort(() => Math.random() - 0.5)) {
        simulateRandomClick(img);
        await randomDelay();

        const fixedContainer = document.querySelector(".fixed.inset-0.overflow-y-auto");
        if (fixedContainer) {
          await handleFixedElement();
          await randomDelay();
          await handleMoonSeekers();
        }
      }
    }
  }
}

// Phần 2: Hàm xử lý cây ăn quả, cây khô và đất trống
async function handleFruitHarvesting() {
  const allImages = Array.from(document.querySelectorAll("img"));

  // Thu hoạch cây khô
  const dryTrees = allImages.filter(img => dryTreeSources.includes(img.src));
  for (const img of dryTrees.sort(() => Math.random() - 0.5)) {
    simulateRandomClick(img);
    await randomDelay();
  }

  // Thu hoạch cây chín loại 1
  if (fruitType1 === 1) {
    const ripe1 = allImages.filter(img => fruitType1List.includes(img.src));
    for (const img of ripe1.sort(() => Math.random() - 0.5)) {
      simulateRandomClick(img);
      await randomDelay();
    }
  }

  // Thu hoạch cây chín loại 2
  if (fruitType2 === 1) {
    for (const src of fruitType2List) {
      const matches = allImages.filter(img => img.src === src);
      for (const img of matches) {
        simulateRandomClick(img);
        await randomDelay();
      }
    }
  }

  // Trồng lại trái cây nếu có đất trống và có seed order
  const soilImgs = allImages.filter(img => img.src === targetSrc4);
  if (soilImgs.length > 0 && fruitSeedOrderArray.length > 0) {
    const seedSelected = await selectFruitSeed(fruitSeedOrderArray);
    if (seedSelected) {
      for (const img of soilImgs.sort(() => Math.random() - 0.5)) {
        simulateRandomClick(img);
        await randomDelay();
      }
    }
  }
}

async function selectFruitSeed(order) {
  const marketBtn = document.querySelector("img[src*='betty.gif']");
  if (!marketBtn) return false;

  simulateRandomClick(marketBtn);
  await randomDelay();

  const seedEls = document.querySelectorAll(
    ".fixed.inset-0.overflow-y-auto .flex.flex-wrap.mb-2 > .relative"
  );

  for (let idx of order) {
    const el = seedEls[idx - 1];
    if (!el) continue;

    const stockDiv = el.querySelector(".w-fit.justify-center.flex.items-center.text-xs");
    if (!stockDiv || stockDiv.textContent.trim() === "0") continue;

    const seedInfo = fruitSeeds[idx - 1];
    if (!seedInfo) continue;

    // Kiểm tra xem loại seed này có được bật không
    if ((seedInfo.type === 1 && fruitType1 !== 1) ||
        (seedInfo.type === 2 && fruitType2 !== 1)) {
      continue;
    }

    const img = el.querySelector(`img[src='${seedInfo.image}']`);
    if (!img) continue;

    simulateRandomClick(img);
    await randomDelay();

    const closeBtn = document.querySelector(
      "img[src*='close.png'], .fixed button[class*='close']"
    );
    if (closeBtn) {
      closeBtn.click();
      await randomDelay();
    }
    return true;
  }

  // Đóng market nếu không tìm thấy seed phù hợp
  const closeBtn = document.querySelector(
    "img[src*='close.png'], .fixed button[class*='close']"
  );
  if (closeBtn) {
    closeBtn.click();
    await randomDelay();
  }
  return false;
}

// Hàm chọn hạt giống theo thứ tự
async function selectSeed(seedOrder) {
  const marketImage = document.querySelector("img[src*='betty.gif']");
  if (!marketImage) {
    return false;
  }

  const selectAndCloseMarket = async () => {
    simulateRandomClick(marketImage);
    await randomDelay();

    const seedElements = document.querySelectorAll(
      ".fixed.inset-0.overflow-y-auto .flex.flex-wrap.mb-2 > .relative"
    );
    if (seedElements.length === 0) {
      return false;
    }

    for (let seedIndex of seedOrder) {
      let seedElement = seedElements[seedIndex - 1];
      if (!seedElement) {
        continue;
      }

      let stockDiv = seedElement.querySelector(
        ".w-fit.justify-center.flex.items-center.text-xs"
      );
      if (!stockDiv || stockDiv.textContent.trim() === "0") {
        continue;
      }

      let cropImage = seedElement.querySelector("img[src*='crop.png']");
      if (!cropImage) {
        continue;
      }

      simulateRandomClick(cropImage);
      await randomDelay();

      const closeButton = document.querySelector(
        "img[src*='close.png'], img[src*='icons/close'], .fixed button[class*='close'], .fixed img[alt*='close' i]"
      );
      if (closeButton) {
        closeButton.click();
        await randomDelay();
        return true;
      }
      return false;
    }
    return false;
  };

  let success = await selectAndCloseMarket();
  while (!success) {
    success = await selectAndCloseMarket();
  }

  return true;
}

// Hàm xử lý menu đặc biệt
async function handleFixedElement() {
  const parent = document.querySelector(".fixed .relative.w-full.rounded-md");
  if (!parent) return;

  const imgs = parent.querySelectorAll("img");
  if (imgs.length < 2) return;

  const img2 = imgs[1];
  simulateRandomClick(img2);
  clickCloseButton();
}

// Hàm xử lý Moon Seekers
async function handleMoonSeekers() {
  const moonSeekersSpan = document.querySelector(
    ".fixed .flex.flex-col.justify-center span.text-center.mb-2"
  );

  if (moonSeekersSpan) {
    const text = moonSeekersSpan.textContent.toLowerCase();
    let validSizes = [];
    if (text.includes("moon seekers")) {
      validSizes = [
        { width: 13, height: 16 },
        { width: 12, height: 16 },
        { width: 16, height: 17 },
        { width: 15, height: 16 },
        { width: 15, height: 17 },
        { width: 96, height: 64 },
        { width: 18, height: 16 },
        { width: 22, height: 25 },
        { width: 18, height: 29 },
        { width: 20, height: 19 },
        { width: 33, height: 28 },
        { width: 29, height: 28 },
        { width: 25, height: 25 },
      ];
    } else if (text.includes("goblins")) {
      validSizes = [
        { width: 96, height: 64 },
        { width: 18, height: 16 },
        { width: 22, height: 25 },
        { width: 18, height: 29 },
        { width: 20, height: 19 },
        { width: 33, height: 28 },
        { width: 29, height: 28 },
        { width: 25, height: 25 },
        { width: 26, height: 21 },
        { width: 18, height: 12 },
        { width: 25, height: 27 },
        { width: 24, height: 21 },
        { width: 19, height: 21 },
      ];
    }

    if (validSizes.length > 0) {
      const images = document.querySelectorAll(
        ".fixed .flex.flex-col.justify-center .flex.flex-wrap.justify-center.items-center img"
      );

      for (let img of images) {
        const width = img.naturalWidth;
        const height = img.naturalHeight;

        if (
          validSizes.some(
            (size) => size.width === width && size.height === height
          )
        ) {
          simulateRandomClick(img);
        }
      }
    }

    clickCloseButton();
  }
}

// Hàm đóng menu đặc biệt
function clickCloseButton() {
  setTimeout(() => {
    const closeButton = Array.from(
      document.querySelectorAll(".fixed button")
    ).find((btn) => btn.textContent.trim() === "Close");

    if (closeButton) {
      simulateRandomClick(closeButton);
    }
  }, 500);
}

// Chạy chương trình
startRandomInterval();
